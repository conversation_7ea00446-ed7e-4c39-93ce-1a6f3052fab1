{"name": "snaptee", "version": "1.0.0", "description": "a backend project", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/HatimAshraf/snaptee.git"}, "keywords": ["jaascript"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/Hatim<PERSON>hraf/snaptee/issues"}, "homepage": "https://github.com/HatimAshraf/snaptee#readme", "devDependencies": {"nodemon": "^3.1.10", "prettier": "^3.6.2"}, "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "mongoose-aggregate-paginate-v2": "^1.1.4", "multer": "^2.0.2"}}