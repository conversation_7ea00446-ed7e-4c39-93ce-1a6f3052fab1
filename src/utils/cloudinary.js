import fs from "fs";
import { v2 as cloudinary } from "cloudinary";
import dotenv from "dotenv";
dotenv.config();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const uploadOnCloudinary = async (path) => {
  try {
    if (!path) {
      console.log("path is not provided");
      return null;
    }
    const uploadresult = await cloudinary.uploader.upload(path, {
      resource_type: "auto",
    });
    console.log("file is uploaded successfully at: ", uploadresult.url);
    return uploadresult;
  } catch (error) {
    fs.unlinkSync(path);
    console.log("error in uploading file: ", error);
    return null;
  } finally {
    fs.unlinkSync(path);
  }
};

export { uploadOnCloudinary };

/*
(async function() {

    // Configuration
    cloudinary.config({ 
        cloud_name: 'adusalaa', 
        api_key: '412972438722915', 
        api_secret: '<your_api_secret>' // Click 'View API Keys' above to copy your API secret
    });
    
    // Upload an image
     const uploadResult = await cloudinary.uploader
       .upload(
           'https://res.cloudinary.com/demo/image/upload/getting-started/shoes.jpg', {
               public_id: 'shoes',
           }
       )
       .catch((error) => {
           console.log(error);
       });
    
    console.log(uploadResult);
    
    // Optimize delivery by resizing and applying auto-format and auto-quality
    const optimizeUrl = cloudinary.url('shoes', {
        fetch_format: 'auto',
        quality: 'auto'
    });
    
    console.log(optimizeUrl);
    
    // Transform the image: auto-crop to square aspect_ratio
    const autoCropUrl = cloudinary.url('shoes', {
        crop: 'auto',
        gravity: 'auto',
        width: 500,
        height: 500,
    });
    
    console.log(autoCropUrl);    
})();
*/
